{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["mcp__gmail__gmail_send_email", "Bash(grep:*)", "mcp__context7__resolve-library-id", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(pkill:*)", "mcp__context7__get-library-docs", "Bash(bin/rails:*)", "<PERSON><PERSON>(curl:*)", "Bash(ruby test:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(bundle exec rails:*)", "<PERSON><PERSON>(rails runner:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(bundle exec rspec:*)", "Bash(./test_vite_configuration.rb)", "Bash(./bin/vite-health-check)", "Bash(RAILS_ENV=test bundle exec rails db:reset)", "Bash(RAILS_ENV=test bundle exec rails db:migrate)", "Bash(RAILS_ENV=test bundle exec rails runner '\nFactoryBot.reload\nuser = FactoryBot.create(:user)\nproject = FactoryBot.create(:project, :minimal, user: user) \n\nproject.private_files.attach(\n  io: File.open(Rails.root.join(\"spec\", \"fixtures\", \"files\", \"test.pdf\")),\n  filename: \"test.pdf\",\n  content_type: \"application/pdf\"\n)\n\nputs \"Project ID: #{project.id}\"\nputs \"User ID: #{user.id}\"\nputs \"Files attached: #{project.private_files.count}\"\n\nif project.private_files.any?\n  pdf_file = project.private_files.first\n  puts \"PDF file ID: #{pdf_file.id}\"\n  puts \"PDF file content type: #{pdf_file.content_type}\"\n  \n  begin\n    hash = project.generate_secure_file_hash(pdf_file)\n    puts \"Generated hash: #{hash}\"\n    puts \"Hash length: #{hash.length}\"\n    \n    found_file = project.find_file_by_secure_hash(hash)\n    puts \"Found file by hash: #{found_file&.id}\"\n    puts \"Hash lookup works: #{found_file == pdf_file}\"\n  rescue => e\n    puts \"Error: #{e.message}\"\n    puts e.backtrace.first(3)\n  end\nend\n')", "Bash(RAILS_ENV=test bundle exec rails db:drop db:create db:migrate)", "Bash(RAILS_ENV=test bundle exec rails runner '\nFactoryBot.reload\nuser = FactoryBot.create(:user)\nproject = FactoryBot.create(:project, :minimal, :with_files, user: user) \n\nputs \"After factory creation:\"\nputs \"Project ID: #{project.id}\"\nputs \"Files count: #{project.private_files.count}\"\n\nif project.private_files.any?\n  project.private_files.each_with_index do |file, i|\n    puts \"  File #{i+1}: #{file.filename} (#{file.content_type})\"\n  end\nend\n\n# Reload and check again\nproject.reload\nputs \"After reload:\"\nputs \"Files count: #{project.private_files.count}\"\n\n# Check from database directly\nfresh_project = Project.find(project.id)  \nputs \"Fresh from DB:\"\nputs \"Files count: #{fresh_project.private_files.count}\"\n')", "Bash(RAILS_ENV=test ruby debug_factory.rb)", "mcp__gemini__chat", "mcp__gemini__debug", "Bash(bundle exec ruby:*)", "mcp__puppeteer__puppeteer_screenshot", "mcp__puppeteer__puppeteer_click", "mcp__puppeteer__puppeteer_evaluate", "mcp__puppeteer__puppeteer_fill", "mcp__gemini__analyze", "<PERSON><PERSON>(sed:*)", "Bash(gh issue create:*)", "Bash(gh project item-add:*)", "Bash(gh project item-list:*)", "Bash(gh issue:*)", "Bash(gh project list:*)", "Bash(node:*)", "Bash(gh project field-list:*)", "Bash(gh project view:*)", "mcp__gemini__thinkdeep", "Bash(aws s3api put-bucket-cors:*)", "Bash(aws s3api get-bucket-cors:*)", "Bash(RUN_S3_TESTS=true bundle exec rspec spec/system/s3_integration/ --format documentation)", "Bash(RUN_S3_TESTS=true bundle exec rspec spec/system/s3_integration/direct_upload_spec.rb:22 --format documentation)", "Bash(RUN_S3_TESTS=true CAPYBARA_DRIVER=selenium_chrome_headless bundle exec rspec spec/system/s3_integration/direct_upload_spec.rb:22 --format documentation)", "Bash(RUN_S3_TESTS=true bundle exec rspec spec/system/s3_integration/direct_upload_spec.rb:28 --format documentation)", "Bash(aws s3api get-bucket-policy:*)", "Bash(./bin/simple_webhook_simulator:*)", "Bash(git add:*)", "<PERSON><PERSON>(touch:*)", "Bash(rg:*)", "<PERSON><PERSON>(claude mcp:*)", "Bash(gh api:*)", "Bash(gh pr list:*)", "Bash(ruby:*)", "mcp__gemini__codereview", "WebFetch(domain:docs.anthropic.com)", "Bash(RAILS_ENV=test bundle exec rails runner test_draft_validations.rb)", "Bash(RAILS_ENV=test bin/rails db:reset)", "Bash(rm:*)", "Bash(git checkout:*)", "mcp__linear__linear_search_issues", "mcp__linear__linear_getTeams", "mcp__linear__linear_createProject", "mcp__linear__linear_getProjects", "mcp__linear__linear_updateProject", "mcp__linear__linear_getWorkflowStates", "mcp__linear__linear_createIssue", "mcp__linear__linear_getIssues", "mcp__linear__linear_searchIssues", "mcp__linear__linear_archiveIssue", "mcp__linear__linear_getIssueById", "mcp__linear__linear_createComment"], "deny": []}}