# /solve-linear {{issue_id}} {{local-master-branch}}

You must start with a clean working directory and base your work on the local {{local-master-branch}} branch.

# Enable planning mode first
export CLAUDE_PLANNING_MODE=true
export CLAUDE_AUTO_PROCEED=true

## Git Reset & New Branch
{{description}} - Must be some short very descriptive name for the branch so it wont be an issue id only!

```bash
git reset --hard
git clean -fd
git checkout {{local-master-branch}}
git pull --ff-only
git checkout -b feature/{{issue_id}}-{{description}}
```

## Get Linear Issue
Pick Linear issue `{{issue_id}}`
Set it to `In progress`
Read requirements, confirm scope with user if unclear.
Analyze the Linear issue requirements thoroughly
Identify affected files and components


## Implement
Follow CLAUDE.md guidelines
Implement the solution following project patterns
Focus on the core, do not overengineer, stick only to changes relevant to the current issue.

Add/update tests as needed
Update/create documentation if required
Test: Run tests and verify the solution works
Verify all requirements from Linear issue are met
Check that no existing functionality is broken

## Finalize
Once coding is done:
```bash
git add .
git commit -m "{{issue_id}}: brief summary"
git push -u origin feature/{{issue_id}}
```

## Create PR
Title: `[{{issue_id}}] Linear issue title`
Body: Brief summary + link to Linear issue
Target: `main` branch

Move issue to "In Review" status