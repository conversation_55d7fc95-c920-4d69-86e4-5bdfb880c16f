import { initializeAutocomplete } from '../js/google_maps_autocomplete';
import '../js/upload_handler';
import '../../javascript/autosave';

// Initialize Action Cable
import { createConsumer } from '@rails/actioncable';
window.App = window.App || {};
window.App.cable = createConsumer();

document.addEventListener('DOMContentLoaded', () => {
  const locationInputs = ['location', 'project_location'];
  const fieldsExist = locationInputs.some(id => document.getElementById(id));

  if (fieldsExist) {
    const apiKey = document.querySelector('meta[name="google-maps-api-key"]').getAttribute('content');
    let googleMapsLoaded = false;
    let googleMapsLoadPromise = null;
    
    // Function to load Google Maps API only when needed (iOS Safari fix)
    function loadGoogleMapsAPI() {
      if (googleMapsLoadPromise) {
        return googleMapsLoadPromise;
      }
      
      googleMapsLoadPromise = new Promise((resolve, reject) => {
        if (googleMapsLoaded) {
          resolve();
          return;
        }
        
        const script = document.createElement('script');
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
        script.async = true;
        script.defer = true;
        
        script.onload = function() {
          googleMapsLoaded = true;
          resolve();
        };
        
        script.onerror = function() {
          reject(new Error('Failed to load Google Maps API'));
        };
        
        document.head.appendChild(script);
      });
      
      return googleMapsLoadPromise;
    }
    
    // Set up lazy initialization for each location input to prevent iOS autoscroll issue
    locationInputs.forEach(id => {
      const input = document.getElementById(id);
      if (input && !input.hasAttribute('data-autocomplete-ready')) {
        input.setAttribute('data-autocomplete-ready', 'true');
        
        // Initialize autocomplete only when user actually focuses the location field
        input.addEventListener('focus', async function() {
          if (!this.hasAttribute('data-autocomplete-initialized')) {
            try {
              await loadGoogleMapsAPI();
              initializeAutocomplete(id);
              this.setAttribute('data-autocomplete-initialized', 'true');
            } catch (error) {
              console.error('Failed to initialize location autocomplete:', error);
            }
          }
        }, { once: true }); // Use { once: true } to ensure it only runs once
      }
    });
  }
});

document.addEventListener('DOMContentLoaded', () => {
  const toggle = document.querySelector('#navbarToggle')
  const menu = document.querySelector('#navbarMenu')
  const sidebar = document.querySelector('.sidebar')
  
  if (toggle && menu) {
    toggle.addEventListener('click', (event) => {
      event.stopPropagation(); // Prevent click from bubbling to document
      toggle.classList.toggle('active')
      
      // Desktop and mobile have different behaviors
      if (window.innerWidth <= 768) {
        // On mobile, this button toggles the main sidebar
        sidebar.classList.toggle('mobile-visible');
        // The profile menu should remain hidden on mobile as its items are in the sidebar
        menu.classList.add('navbar__menu--hidden');
      } else {
        // On desktop, this button toggles the profile dropdown
        menu.classList.toggle('navbar__menu--hidden');
      }
    })
  }
  
  // Close menus when clicking outside
  document.addEventListener('click', (event) => {
    // Close mobile sidebar when clicking outside
    if (sidebar && sidebar.classList.contains('mobile-visible') && 
        !sidebar.contains(event.target) && 
        !toggle.contains(event.target)) {
      sidebar.classList.remove('mobile-visible')
      toggle.classList.remove('active')
    }
    
    // Close desktop profile menu when clicking outside
    if (menu && !menu.classList.contains('navbar__menu--hidden') &&
        !menu.contains(event.target) &&
        !toggle.contains(event.target)) {
      menu.classList.add('navbar__menu--hidden')
      toggle.classList.remove('active')
    }
  })
  
  // Update sidebar visibility on window resize
  window.addEventListener('resize', () => {
    if (sidebar && window.innerWidth > 768) {
      sidebar.classList.remove('mobile-visible')
      // Also ensure profile menu is hidden if it was open
      if (menu && !menu.classList.contains('navbar__menu--hidden')) {
        menu.classList.add('navbar__menu--hidden');
        toggle.classList.remove('active');
      }
    }
  })
})

document.addEventListener('DOMContentLoaded', function() {
  document.querySelectorAll('[data-confirm]').forEach(function(element) {
    element.addEventListener('click', function(event) {
      var message = element.getAttribute('data-confirm');
      if (!confirm(message)) {
        event.preventDefault();
      }
    });
  });
});

document.addEventListener('DOMContentLoaded', function() {
  const countElement = document.getElementById('connectionRequestsCount');
  // Only proceed if the countElement is present, indicating a logged-in user context
  if (countElement) {
    fetch('/connection_requests/count')
      .then(response => response.json())
      .then(data => {
        if (data.count > 0) {
          countElement.textContent = data.count;
          countElement.style.display = 'inline';
        }

        const myConnectionsBadge = document.getElementById('myConnectionsBadge');
        if (myConnectionsBadge && data.has_network_requests) {
          myConnectionsBadge.style.display = 'inline';
        }

        const myProjectsBadge = document.getElementById('myProjectsBadge');
        if (myProjectsBadge && data.has_project_requests) {
          myProjectsBadge.style.display = 'inline';
        }
      })
      .catch(error => console.error('Error fetching connection requests count:', error));
  }
});

document.addEventListener('click', function(e) {
  if (e.target.matches('.modal-trigger')) {
    e.preventDefault(); 
    const url = e.target.getAttribute('href'); 
    fetch(url)
      .then(response => response.text())
      .then(html => {
        document.getElementById('modalContent').innerHTML = html;
        document.getElementById('customModal').classList.remove('hidden');
      })
    .catch(err => console.error('Error loading modal content:', err));
  }
  
  if (e.target.matches('.close-button') || e.target.matches('#customModal')) {
    document.getElementById('customModal').classList.add('hidden');
  }
});

// Secure File Display System (Chunk 8)
class SecureFileViewer {
  constructor() {
    this.lightbox = document.getElementById('secureLightbox');
    this.lightboxBody = this.lightbox?.querySelector('.lightbox-body');
    this.lightboxLoading = this.lightbox?.querySelector('.lightbox-loading');
    this.downloadBtn = this.lightbox?.querySelector('.lightbox-download');
    this.currentToken = null;
    this.currentFileHash = null;
    
    this.initEventListeners();
  }
  
  initEventListeners() {
    // File preview clicks
    // File preview clicks - COMMENTED OUT TO PREVENT MODAL CONFLICT
    // document.addEventListener('click', (e) => {
    //   const fileItem = e.target.closest('.file-item');
    //   if (fileItem && (e.target.closest('[data-action="preview"]') || e.target.classList.contains('file-thumbnail-placeholder'))) {
    //     this.openSecurePreview(fileItem);
    //   }
    // });
    
    // Lightbox close
    if (this.lightbox) {
      this.lightbox.querySelector('.lightbox-close')?.addEventListener('click', () => this.closeLightbox());
      this.lightbox.querySelector('.lightbox-backdrop')?.addEventListener('click', () => this.closeLightbox());
      this.downloadBtn?.addEventListener('click', () => this.downloadCurrentFile());
    }
    
    // ESC key to close
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && !this.lightbox?.classList.contains('hidden')) {
        this.closeLightbox();
      }
    });
  }
  
  async openSecurePreview(fileItem) {
    const fileHash = fileItem.dataset.fileHash;
    const contentType = fileItem.dataset.contentType;
    const projectId = fileItem.dataset.projectId;
    
    if (!fileHash || !projectId) {
      console.error('Missing file identification data');
      return;
    }
    
    // Show lightbox with loading state
    this.showLightbox();
    this.showLoading();
    
    try {
      // Request secure token
      // Use current page locale for API requests
      const currentLocale = window.location.pathname.split('/')[1] || 'sk';
      const tokenResponse = await fetch(`/${currentLocale}/projects/${projectId}/request_file_token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        },
        body: JSON.stringify({ file_hash: fileHash })
      });
      
      if (!tokenResponse.ok) {
        throw new Error('Failed to get file access token');
      }
      
      const tokenData = await tokenResponse.json();
      this.currentToken = tokenData.token;
      this.currentFileHash = fileHash;
      
      // Display content based on type
      if (contentType.includes('image')) {
        await this.displaySecureImage(tokenData.token);
      } else if (contentType === 'application/pdf') {
        await this.displaySecurePDF(tokenData.token);
      } else {
        this.showUnsupportedType();
      }
      
      // Show download button
      this.downloadBtn.style.display = 'inline-flex';
      
    } catch (error) {
      console.error('Secure preview failed:', error);
      this.showError('Failed to load secure content');
    }
  }
  
  async displaySecureImage(token) {
    const img = new Image();
    const secureUrl = `/secure/stream?t=${token}`;
    
    img.onload = () => {
      this.lightboxBody.innerHTML = '';
      this.lightboxBody.appendChild(img);
    };
    
    img.onerror = () => {
      this.showError('Failed to load image');
    };
    
    img.src = secureUrl;
  }
  
  async displaySecurePDF(token) {
    const secureUrl = `/secure/stream?t=${token}#toolbar=0&navpanes=0&scrollbar=1&view=FitH`;
    const iframe = document.createElement('iframe');
    iframe.src = secureUrl;
    iframe.style.width = '80vw';
    iframe.style.height = '70vh';
    
    // CRITICAL SECURITY FIX: Add sandbox attribute to prevent XSS attacks.
    // This prevents malicious files from accessing parent window resources.
    // Allows scripts and plugins for PDF interactivity but omits 'allow-same-origin'
    // to prevent the iframe from accessing parent DOM and cookies.
    iframe.sandbox = 'allow-scripts allow-forms';
    
    this.lightboxBody.innerHTML = '';
    this.lightboxBody.appendChild(iframe);
  }
  
  showLightbox() {
    this.lightbox?.classList.remove('hidden');
    document.body.style.overflow = 'hidden';
  }
  
  closeLightbox() {
    this.lightbox?.classList.add('hidden');
    document.body.style.overflow = '';
    this.downloadBtn.style.display = 'none';
    this.currentToken = null;
    this.currentFileHash = null;
  }
  
  showLoading() {
    this.lightboxBody.innerHTML = '<div class="lightbox-loading"><div class="loading-spinner"></div><p>Loading secure content...</p></div>';
  }
  
  showError(message) {
    this.lightboxBody.innerHTML = `<div class="lightbox-error"><p>${message}</p></div>`;
  }
  
  showUnsupportedType() {
    this.lightboxBody.innerHTML = '<div class="lightbox-error"><p>Preview not available for this file type</p></div>';
  }
  
  
  async downloadCurrentFile() {
    if (!this.currentToken) return;
    
    try {
      const response = await fetch(`/secure/stream?t=${this.currentToken}`);
      const blob = await response.blob();
      
      // UX IMPROVEMENT: Generate filename with proper extension based on content type
      const extension = getFileExtensionFromContentType(blob.type);
      const filename = `secure_file_${Date.now()}.${extension}`;
      
      // Create download without exposing original filename
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      a.click();
      
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
    }
  }
}

// Secure Download Handler - replaces old direct download (Chunk 9)
document.addEventListener('click', async function(event) {
  if (event.target.matches('.downloadButton') || event.target.closest('.downloadButton')) {
    event.preventDefault();
    event.stopPropagation(); // Prevent event bubbling to thumbnail container
    
    const button = event.target.closest('.downloadButton');
    const fileHash = button.dataset.fileHash;
    const projectId = button.dataset.projectId;
    
    // Check if this is a sidebar download
    const isSidebarDownload = button.closest('#sidebar-thumbnail-list');
    const overlay = isSidebarDownload 
      ? document.getElementById('sidebar-download-overlay')
      : document.getElementById('downloadOverlay');
    
    if (!fileHash || !projectId) {
      console.error('Missing download parameters');
      return;
    }
    
    overlay.style.display = 'flex';
    
    try {
      // Request secure token
      // Use current page locale for API requests
      const currentLocale = window.location.pathname.split('/')[1] || 'sk';
      const tokenResponse = await fetch(`/${currentLocale}/projects/${projectId}/request_file_token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        },
        body: JSON.stringify({ file_hash: fileHash })
      });
      
      if (!tokenResponse.ok) {
        throw new Error('Failed to get download token');
      }
      
      const tokenData = await tokenResponse.json();
      
      // Download using secure token
      const response = await fetch(`/secure/stream?t=${tokenData.token}`);
      if (!response.ok) throw new Error('Download failed');
      
      const blob = await response.blob();
      
      // UX IMPROVEMENT: Generate filename with proper extension based on content type
      const extension = getFileExtensionFromContentType(tokenData.content_type);
      const filename = `secure_file_${Date.now()}.${extension}`;
      
      const blobUrl = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = blobUrl;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      
      window.URL.revokeObjectURL(blobUrl);
      document.body.removeChild(a);
      
    } catch (error) {
      console.error('Secure download failed:', error);
    } finally {
      overlay.style.display = 'none';
    }
  }
});

// Helper function to determine file extension from content type
function getFileExtensionFromContentType(contentType) {
  const mimeToExtension = {
    'application/pdf': 'pdf',
    'image/jpeg': 'jpg',
    'image/jpg': 'jpg',
    'image/png': 'png',
    'image/gif': 'gif',
    'image/svg+xml': 'svg',
    'image/webp': 'webp',
    'text/plain': 'txt',
    'text/csv': 'csv',
    'application/json': 'json',
    'application/xml': 'xml',
    'application/zip': 'zip',
    'application/x-zip-compressed': 'zip',
    'application/msword': 'doc',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
    'application/vnd.ms-excel': 'xls',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
    'application/vnd.ms-powerpoint': 'ppt',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx'
  };
  
  return mimeToExtension[contentType] || 'bin';
}

// Inline File Display System (Chunk 2)
class InlineFileViewer {
  constructor() {
    this.inlineViewer = document.getElementById('inline-file-viewer');
    this.viewerTitle = document.getElementById('inline-viewer-title');
    this.viewerContent = document.getElementById('inline-viewer-content');
    this.closeBtn = document.getElementById('close-inline-viewer');
    this.fileGrid = document.querySelector('.file-grid');
    this.mainContent = document.querySelector('.main-content');
    this.sideRight = null; // Will be found dynamically
    this.thumbnailsCloned = false;
    this.currentFileId = null;
    
    this.initEventListeners();
  }
  
  initEventListeners() {
    // Handle thumbnail clicks for inline viewing
    document.addEventListener('click', (e) => {
      const thumbnail = e.target.closest('.file-thumbnail[data-action="inline"]');
      if (thumbnail) {
        e.preventDefault();
        this.openInlineView(thumbnail);
      }
    });
    
    // Handle close button
    if (this.closeBtn) {
      this.closeBtn.addEventListener('click', () => this.closeInlineView());
    }
  }
  
  async openInlineView(thumbnail) {
    const inlineUrl = thumbnail.dataset.inlineUrl;
    const fileItem = thumbnail.closest('.file-item');
    const fileName = fileItem.querySelector('.file-name')?.textContent?.trim() || 'File';
    const contentType = fileItem.dataset.contentType;
    const fileId = fileItem.dataset.fileId;
    
    if (!inlineUrl) {
      console.error('Missing inline URL');
      return;
    }
    
    // Store current file ID
    this.currentFileId = fileId;
    
    // Clone thumbnails to sidebar on first open
    if (!this.thumbnailsCloned) {
      this.setupSidebarThumbnails();
    }
    
    // Activate the viewer layout
    this.activateViewerLayout();
    
    // Show the inline viewer
    this.showInlineViewer();
    this.viewerTitle.textContent = fileName;
    this.showLoading();
    
    // Set active thumbnail in sidebar
    this.setActiveThumbnail(fileId);
    
    try {
      if (contentType.startsWith('image/')) {
        this.displayInlineImage(inlineUrl, fileName);
      } else if (contentType === 'application/pdf') {
        this.displayInlinePDF(inlineUrl, fileName);
      } else {
        this.showUnsupportedType();
      }
    } catch (error) {
      console.error('Inline viewing failed:', error);
      this.showError('Failed to load file for inline viewing');
    }
  }
  
  setupSidebarThumbnails() {
    // Find the side-right area in the files section
    const filesSection = this.fileGrid?.closest('.file-viewer-container');
    this.sideRight = filesSection?.querySelector('.side-right');
    
    if (!this.sideRight || !this.fileGrid) return;
    
    // Create sidebar wrapper for overlay containment
    const sidebarWrapper = document.createElement('div');
    sidebarWrapper.className = 'sidebar-thumbnail-wrapper';
    
    // Create sidebar thumbnail list container
    const sidebarList = document.createElement('div');
    sidebarList.id = 'sidebar-thumbnail-list';
    sidebarList.className = 'sidebar-thumbnail-list';
    
    // Create sidebar download overlay with localized text
    const sidebarOverlay = document.createElement('div');
    sidebarOverlay.id = 'sidebar-download-overlay';
    sidebarOverlay.className = 'sidebar-download-overlay';
    sidebarOverlay.style.display = 'none';
    
    // Get localized text
    const translations = document.getElementById('js-translations');
    const downloadingText = translations ? 
      translations.dataset.downloadingText : 
      'Loading file.';
    
    sidebarOverlay.innerHTML = `<span>${downloadingText} <span class="loading-dots"></span></span>`;
    
    // Clone thumbnails to sidebar
    const fileItems = this.fileGrid.querySelectorAll('.file-item');
    fileItems.forEach(item => {
      const clone = item.cloneNode(true);
      const fileHash = item.dataset.fileHash;
      const projectId = item.dataset.projectId;
      
      // Remove original download link from clone
      const originalDownloadLink = clone.querySelector('.file-actions');
      if (originalDownloadLink) originalDownloadLink.remove();
      
      // Add new download link after file meta using template
      const fileMeta = clone.querySelector('.file-meta');
      if (fileMeta && fileHash && projectId) {
        // Get the download link template
        const template = document.getElementById('sidebar-download-template');
        if (template) {
          const downloadLinkClone = template.content.cloneNode(true);
          const downloadLink = downloadLinkClone.querySelector('.sidebar-download-link');
          
          // Set the data attributes
          downloadLink.dataset.fileHash = fileHash;
          downloadLink.dataset.projectId = projectId;
          
          // Insert after file meta
          fileMeta.parentNode.insertBefore(downloadLinkClone, fileMeta.nextSibling);
        }
      }
      
      sidebarList.appendChild(clone);
    });
    
    // Add event delegation for sidebar thumbnail clicks only
    sidebarList.addEventListener('click', (e) => {
      // Check if click is on thumbnail container or its children
      const thumbnailContainer = e.target.closest('.file-thumbnail-container');
      if (thumbnailContainer) {
        const fileItem = thumbnailContainer.closest('.file-item');
        if (fileItem) {
          const thumbnail = fileItem.querySelector('.file-thumbnail[data-action="inline"]');
          if (thumbnail) {
            this.openInlineView(thumbnail);
          }
        }
      }
    });
    
    // Assemble the wrapper structure
    sidebarWrapper.appendChild(sidebarList);
    sidebarWrapper.appendChild(sidebarOverlay);
    this.sideRight.appendChild(sidebarWrapper);
    this.thumbnailsCloned = true;
  }
  
  activateViewerLayout() {
    // Find the parent flex container
    const flexContainer = this.fileGrid?.closest('.file-viewer-container');
    if (flexContainer) {
      flexContainer.classList.add('viewer-active');
    }
  }
  
  deactivateViewerLayout() {
    // Find the parent flex container
    const flexContainer = this.fileGrid?.closest('.file-viewer-container');
    if (flexContainer) {
      flexContainer.classList.remove('viewer-active');
    }
  }
  
  setActiveThumbnail(fileId) {
    // Remove active class from all thumbnails in sidebar
    const sidebarList = document.getElementById('sidebar-thumbnail-list');
    if (sidebarList) {
      sidebarList.querySelectorAll('.file-item').forEach(item => {
        item.classList.remove('active');
      });
      
      // Add active class to current file
      const activeItem = sidebarList.querySelector(`[data-file-id="${fileId}"]`);
      if (activeItem) {
        activeItem.classList.add('active');
      }
    }
  }
  
  displayInlineImage(url, fileName) {
    const img = new Image();
    
    img.onload = () => {
      this.viewerContent.innerHTML = '';
      img.alt = fileName;
      img.style.maxWidth = '100%';
      img.style.height = 'auto';
      this.viewerContent.appendChild(img);
    };
    
    img.onerror = () => {
      this.showError('Failed to load image');
    };
    
    img.src = url;
  }
  
  displayInlinePDF(url, fileName) {
    const iframe = document.createElement('iframe');
    iframe.src = `${url}#toolbar=0&navpanes=0&scrollbar=1&view=FitH`;
    iframe.style.width = '100%';
    iframe.style.height = '600px';
    iframe.style.border = 'none';
    iframe.style.borderRadius = '4px';
    iframe.title = fileName;
    
    this.viewerContent.innerHTML = '';
    this.viewerContent.appendChild(iframe);
  }
  
  showInlineViewer() {
    if (this.inlineViewer) {
      this.inlineViewer.style.display = 'block';
      // Don't scroll since we're reorganizing the layout
    }
  }
  
  closeInlineView() {
    if (this.inlineViewer) {
      this.inlineViewer.style.display = 'none';
      this.viewerContent.innerHTML = '';
      this.deactivateViewerLayout();
    }
  }
  
  showLoading() {
    this.viewerContent.innerHTML = '<div style="text-align: center; padding: 2rem;"><p>Loading file...</p></div>';
  }
  
  showError(message) {
    this.viewerContent.innerHTML = `<div style="text-align: center; padding: 2rem; color: #dc3545;"><p>${message}</p></div>`;
  }
  
  showUnsupportedType() {
    this.viewerContent.innerHTML = '<div style="text-align: center; padding: 2rem; color: #6c757d;"><p>Inline preview not available for this file type</p></div>';
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  new SecureFileViewer();
  new InlineFileViewer();
});

document.addEventListener('DOMContentLoaded', function() {
  const phoneInput = document.getElementById('user_profile_phone');
  if (phoneInput) {
    phoneInput.addEventListener('input', function(e) {
      let value = e.target.value.replace(/\D/g, '');
      if (value.length > 0) {
        // Basic formatting - adjust pattern as needed
        if (value.length <= 3) {
          value = '+' + value;
        } else if (value.length <= 6) {
          value = '+' + value.substring(0, 3) + ' ' + value.substring(3);
        } else if (value.length <= 9) {
          value = '+' + value.substring(0, 3) + ' ' + value.substring(3, 6) + ' ' + value.substring(6);
        } else {
          value = '+' + value.substring(0, 3) + ' ' + value.substring(3, 6) + ' ' + value.substring(6, 9) + ' ' + value.substring(9);
        }
      }
      e.target.value = value;
    });
  }
});

document.addEventListener('DOMContentLoaded', () => {
  const currentLanguage = document.getElementById('currentLanguage');
  const languageMenu = document.getElementById('languageMenu');

  if (currentLanguage && languageMenu) {
    // Toggle dropdown on click
    currentLanguage.addEventListener('click', (event) => {
      event.stopPropagation();
      languageMenu.classList.toggle('hidden');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', (event) => {
      if (!currentLanguage.contains(event.target) && !languageMenu.contains(event.target)) {
        languageMenu.classList.add('hidden');
      }
    });

    // Update current language display when selecting a new language
    languageMenu.querySelectorAll('.language-dropdown__item').forEach(link => {
      link.addEventListener('click', () => {
        const newLanguage = link.textContent;
        currentLanguage.textContent = newLanguage;
        languageMenu.classList.add('hidden');
      });
    });
  }
});