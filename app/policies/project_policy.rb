class ProjectPolicy < ApplicationPolicy
  
  # ApplicationPolicy uses a user object for authorization purposes
  # Policies are to be described clearly 
  # Use granular methods to define permissions\
  # False negative is better than false positive in private show and files access
  
  def index?
    # Everyone can see index, but what they see is controlled in scopes
    true
  end

  def show?
    # Can see full details if:
    # 1. Owner of project (even if not approved)
    # 2. Has explicit ProjectAuth AND project is approved
    #   ProjectAuth has 4 levels: no_access, pending, summary, full. 
    #   Only full allows any access to private resources
    return true if record.user_id == user.id
    return false unless record.approved?
    
    record.project_auths.exists?(user_id: user.id, access_level: 'full_details')
  end

  #TODO: Both policies are identical. Check if needed both
  def view_full_details?
    # Owner always has access (even if not approved)
    return true if record.user_id == user.id
    
    # Project must be approved for any non-owner access
    return false unless record.approved?
    
    # Explicit ProjectAuth grants (existing behavior)
    return true if record.project_auths.exists?(user_id: user.id, access_level: 'full_details')
    
    # NEW: Honor full_access setting for automatic access
    if record.full_access?
      if record.semi_public?
        return true  # All authenticated users get access
      elsif record.network_only?
        return user_connected_to_project_owner?
      end
    end
    
    false
  end

  # def access_files?
  #   # 1. Owner of project
  #   # 2. Has explicit ProjectAuth.
  #   record.user_id == user.id || 
  #     record.project_auths.exists?(user_id: user.id, access_level: 'full_details')
  # end

  def grant_full_details?
    manage_access?
  end

  def deny_access?
    manage_access?
  end

  def delete_access?
    manage_access?
  end

  def manage_access?
    # Ensures only the project owner can approve or deny access
    record.user_id == user.id
  end

  def create?
    # Only logged in users can create projects
    user.present?
  end

  def edit?
    # Only owner of project can edit
    record.user_id == user.id
  end

  def update?
    # Only owner of project can update
    record.user_id == user.id
  end

  def destroy?
    # Only owner of project can destroy
    record.user_id == user.id
  end

  def destroy_file?
    # Only owner of project can destroy
    record.user_id == user.id
  end

  def bulk_delete_files?
    # Only owner of project can bulk delete files
    record.user_id == user.id
  end

  private

  def user_connected_to_project_owner?
    NetworkConnection.where(
      '(inviter_id = ? AND invitee_id = ?) OR (inviter_id = ? AND invitee_id = ?)',
      user.id, record.user_id, record.user_id, user.id
    ).exists?
  end

end