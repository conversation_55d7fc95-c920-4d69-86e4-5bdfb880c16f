class WantPolicy < ApplicationPolicy
  # Check if the user is the owner of the want.
  def owner?
    user == record.user
  end

  # Allow index for all users (you might want to change this based on your app's logic).
  def index?
    true
  end

  # Allow edit only for the owner.
  def edit?
    owner?
  end

  # Allow update only for the owner.
  def update?
    owner?
  end

  # Allow destroy only for the owner.
  def destroy?
    owner?
  end
end 