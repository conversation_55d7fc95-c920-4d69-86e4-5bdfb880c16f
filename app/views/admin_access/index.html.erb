<style>
  .projects-table-container {
    font-family: Arial, sans-serif;
    margin: 20px;
  }
  .projects-table {
    display: flex;
    flex-direction: column;
    border: 1px solid #ccc;
    width: 100%;
  }
  .projects-table-header,
  .project-row {
    display: flex;
    border-bottom: 1px solid #eee;
    align-items: stretch; /* Ensure cells have same height */
  }
  .projects-table-header {
    background-color: #f0f0f0;
    font-weight: bold;
  }
  .project-cell {
    padding: 12px 10px;
    text-align: left;
    overflow: hidden; /* Changed from hidden to visible for wrapping */
    text-overflow: ellipsis;
    /* white-space: nowrap; Removed to allow wrapping */
    border-right: 1px solid #eee;
    display: flex; /* For vertical centering if needed */
    align-items: center; /* For vertical centering if needed */
  }

  /* Specific widths */
  .project-cell.project-id,
  .project-cell.user-id {
    flex: 0 0 80px; /* Narrower fixed width */
    white-space: nowrap; /* Keep these on one line */
  }
  .project-cell.user-email {
    flex: 0 0 200px; /* Wider fixed width */
    white-space: nowrap;
  }
  .project-cell.project-summary {
    flex: 1 1 auto; /* Takes remaining space and allows shrinking/growing */
    white-space: normal; /* Allow text wrapping */
    word-break: break-word; /* Ensure long words break */
    overflow-wrap: break-word; /* Alternate property for word breaking */
  }
  .project-cell.last-updated {
    flex: 0 0 150px;
    white-space: nowrap;
  }
  .project-cell.status-cell {
    flex: 0 0 120px;
    white-space: nowrap;
    text-align: center;
  }
  .project-cell.approval-cell {
    flex: 0 0 180px; /* Adjusted to fit buttons */
    justify-content: center; /* Center content in approval cell */
    text-align: center; /* Center the checkbox */
  }

  /* Status badges */
  .status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
  }
  .status-draft {
    background-color: #fef3c7;
    color: #92400e;
  }
  .status-pending {
    background-color: #dbeafe;
    color: #1e40af;
  }
  .status-published {
    background-color: #d1fae5;
    color: #065f46;
  }

  /* Status summary and filters */
  .status-summary {
    display: flex;
    gap: 20px;
    margin: 20px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
  }
  .status-card {
    flex: 1;
    text-align: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
  }
  .status-card h3 {
    margin: 0 0 5px 0;
    font-size: 24px;
    font-weight: bold;
  }
  .status-card p {
    margin: 0;
    color: #6b7280;
    font-size: 14px;
  }
  .filter-buttons {
    display: flex;
    gap: 10px;
    margin: 20px 0;
  }
  .filter-btn {
    padding: 8px 16px;
    border: 1px solid #d1d5db;
    background: white;
    border-radius: 6px;
    text-decoration: none;
    color: #374151;
    font-size: 14px;
  }
  .filter-btn:hover {
    background: #f3f4f6;
    text-decoration: none;
    color: #374151;
  }
  .filter-btn.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
  }

  .project-cell:last-child {
    border-right: none;
  }
  .project-row:nth-child(even) {
    background-color: #f9f9f9;
  }
  .project-row:hover {
    background-color: #e9e9e9;
  }
</style>

<div class="projects-table-container">
  <h1>Projects</h1>

  <!-- Status Summary -->
  <div class="status-summary">
    <div class="status-card">
      <h3><%= @status_counts[:total] %></h3>
      <p>Total Projects</p>
    </div>
    <div class="status-card">
      <h3><%= @status_counts[:draft] %></h3>
      <p>Drafts</p>
    </div>
    <div class="status-card">
      <h3><%= @status_counts[:pending] %></h3>
      <p>Pending Approval</p>
    </div>
    <div class="status-card">
      <h3><%= @status_counts[:published] %></h3>
      <p>Published</p>
    </div>
  </div>

  <!-- Filter Buttons -->
  <div class="filter-buttons">
    <%= link_to "All", admin_dashboard_path, class: "filter-btn #{'active' if @status_filter.blank?}" %>
    <%= link_to "Drafts", admin_dashboard_path(status: 'draft'), class: "filter-btn #{'active' if @status_filter == 'draft'}" %>
    <%= link_to "Pending", admin_dashboard_path(status: 'pending'), class: "filter-btn #{'active' if @status_filter == 'pending'}" %>
    <%= link_to "Published", admin_dashboard_path(status: 'published'), class: "filter-btn #{'active' if @status_filter == 'published'}" %>
  </div>

  <% if @projects && @projects.any? %>
    <% if @status_filter.present? %>
      <p style="margin: 10px 0; color: #6b7280; font-style: italic;">
        Showing <%= @status_filter.capitalize %> projects (<%= @projects.count %> results)
      </p>
    <% end %>
    <div class="projects-table">
      <div class="projects-table-header">
        <div class="project-cell project-id">Project ID</div>
        <div class="project-cell user-id">User ID</div>
        <div class="project-cell user-email">User Email</div>
        <div class="project-cell project-summary">Summary</div>
        <div class="project-cell last-updated">Last Updated</div>
        <div class="project-cell status-cell">Status</div>
        <div class="project-cell approval-cell">Actions</div>
      </div>

      <% @projects.each do |project| %>
        <div class="project-row">
          <div class="project-cell project-id"><%= project.id %></div>
          <div class="project-cell user-id"><%= project.user_id %></div>
          <div class="project-cell user-email"><%= project.user&.email || "N/A" %></div>
          <div class="project-cell project-summary" title="<%= project.summary %>"><%= project.summary %></div>
          <div class="project-cell last-updated"><%= project.updated_at.strftime("%Y-%m-%d %H:%M") %></div>
          <div class="project-cell status-cell">
            <span class="status-badge status-<%= project.status %>"><%= project.status_label %></span>
          </div>
          <div class="project-cell approval-cell">
            <%= form_with(model: project, url: update_approval_project_path(project), method: :patch, local: true, html: { style: 'display: flex; gap: 5px; justify-content: center; align-items: center;' }) do |form| %>
              <%= form.button "Approve", name: "project[approved]", value: "true", type: :submit, class: 'btn btn-success btn-sm', disabled: project.approved?, style: 'margin: 0;' %>
              <%= form.button "Reject", name: "project[approved]", value: "false", type: :submit, class: 'btn btn-danger btn-sm', disabled: !project.approved?, style: 'margin: 0;' %>
            <% end %>
            <%= form_with(model: project, url: admin_destroy_project_path(project), method: :delete, local: true, data: { confirm: "Are you sure you want to delete this project?" }, html: { style: 'display: inline; margin-left: 5px;' }) do |form| %>
              <%= form.button "Delete", type: :submit, class: 'action-button action-button--reject', style: 'margin: 0;' %>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <p>No projects to display.</p>
  <% end %>
</div> 