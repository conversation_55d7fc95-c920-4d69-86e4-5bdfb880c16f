require 'rails_helper'

RSpec.describe "projects/edit", type: :view do
  let(:project) {
    Project.create!(
      user: nil,
      summary: "MyText",
      public_visible: false,
      summary_only: false,
      full_access: false,
      visibility: 1
    )
  }

  before(:each) do
    assign(:project, project)
  end

  it "renders the edit project form" do
    render

    assert_select "form[action=?][method=?]", project_path(project), "post" do

      assert_select "input[name=?]", "project[user_id]"

      assert_select "textarea[name=?]", "project[summary]"

      assert_select "input[name=?]", "project[public_visible]"

      assert_select "input[name=?]", "project[summary_only]"

      assert_select "input[name=?]", "project[full_access]"

      assert_select "input[name=?]", "project[visibility]"
    end
  end
end
