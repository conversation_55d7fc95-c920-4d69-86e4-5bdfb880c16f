require 'rails_helper'

RSpec.describe "Wants", type: :request do
  describe "GET /index" do
    it "returns http success" do
      get "/wants/index"
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET /edit" do
    it "returns http success" do
      get "/wants/edit"
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET /update" do
    it "returns http success" do
      get "/wants/update"
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET /destroy" do
    it "returns http success" do
      get "/wants/destroy"
      expect(response).to have_http_status(:success)
    end
  end

end
