# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Application Overview

This is a Ruby on Rails 7.0.8 application called "Unlisters App" - a platform for listing and sharing private investment opportunities and projects. Users create profiles, connect with each other through an invitation system, and share project listings with controlled access levels.

## Key Technologies & Dependencies

- **Ruby**: 3.1.2
- **Rails**: 7.0.8+
- **Database**: PostgreSQL
- **Frontend**: Vite with Vue.js 3 components, SCSS for styling
- **Authentication**: Devise with invitation system (devise_invitable)
- **Authorization**: ActionPolicy for permissions
- **File Storage**: Active Storage with AWS S3
- **Geocoding**: Geocoder gem for location services
- **Email**: Resend for transactional emails
- **Background Jobs**: GoodJob with PostgreSQL for reliable job processing
- **Testing**: RSpec for testing framework
- **Internationalization**: Rails I18n with Slovak (sk) as default locale, English (en) fallback

## MANDATORY RAILS-FIRST CONSTRAINT:

**Before suggesting ANY external gems, libraries, or system dependencies:**
1. FIRST: Check if Rails framework has a built-in solution
2. FIRST: Check if Active Record, Active Storage, Active Job, etc. provide the needed functionality  
3. FIRST: Verify the official Rails guides and API documentation
4. ONLY if Rails truly lacks the capability, then suggest external solutions

EXPLICITLY state: "Rails built-in check: [feature] IS/IS NOT available in framework"

**Default assumption**: Rails probably has it built-in. Prove it doesn't before adding dependencies.

Remember: Rails is "batteries included" - use the batteries first.

use context7 MCP to find and read the official documents.

## Core Domain Models

- **User**: Devise-based with invitation system, roles (regular/admin/super_boss), network connections
- **Project**: Main entity with complex project type/category/subcategory enums, geo-location, privacy levels, approval workflow
- **NetworkConnection**: Bidirectional user connections created through invitations
- **ConnectionRequest**: Requests for network connections or project access
- **ProjectAuth**: Authorization levels for project access (summary vs full details)
- **UserProfile**: Extended user information with location data
- **Upload**: Server-side file upload tracking with Ready Flag pattern (Issue #25)


## Writing code
- NEVER make code changes that aren't directly related to the task you're currently assigned. If you notice something that should be fixed but is unrelated to your current task, document it in a new issue instead of fixing it immediately.
- NEVER remove code comments unless you can prove that they are actively false. Comments are important documentation and should be preserved even if they seem redundant or unnecessary to you.
- All code files should start with a brief 2 line comment explaining what the file does. Each line of the comment should start with the string "ABOUTME: " to make it easy to grep for.
- When writing comments, avoid referring to temporal context about refactors or recent changes. Comments should be evergreen and describe the code as it is, not how it evolved or was recently changed.


## ✅ **IMPLEMENTED: Project Sharing System**

**🎉 FUNDAMENTAL ISSUE RESOLVED** - The UI sharing preferences now work exactly as users expect.

### **Sharing Dimensions**
Projects have a **two-dimensional sharing model**:

1. **Visibility** (Who can see the project exists):
   - `network_only`: Only connected users see project in listings
   - `semi_public`: All registered users see project in listings

2. **Detail Level** (What they can see - **NOW WORKING**):
   - `summary_only`: Shows title, location, category only - requires explicit approval for full details
   - `full_access`: **✅ FULLY IMPLEMENTED** - Automatically grants access based on visibility settings

### **✅ IMPLEMENTATION COMPLETE (December 2025)**

**The `full_access` attribute now controls automatic authorization**.

- **UI Shows**: "Share Everything" + "My Network" → ✅ *Connected users see full details automatically*
- **Reality**: Non-owner access works according to project sharing settings
- **Location**: `ProjectPolicy#view_full_details?` at `app/policies/project_policy.rb:26-46`

```ruby
def view_full_details?
  # Owner always has access (even if not approved)
  return true if record.user_id == user.id
  
  # Project must be approved for any non-owner access
  return false unless record.approved?
  
  # Explicit ProjectAuth grants (existing behavior)
  return true if record.project_auths.exists?(user_id: user.id, access_level: 'full_details')
  
  # NEW: Honor full_access setting for automatic access
  if record.full_access?
    if record.semi_public?
      return true  # All authenticated users get access
    elsif record.network_only?
      return user_connected_to_project_owner?
    end
  end
  
  false
end
```

### **✅ Authorization Flow Now Works As Expected**

**Automatic Access** for `full_access` projects:
1. User visits project (no request needed)
2. System checks project settings + user connection status
3. Access granted automatically based on sharing preferences
4. Users see full details immediately

**Manual Approval** for `summary_only` projects (unchanged):
1. User clicks "Request access" 
2. Creates ConnectionRequest + ProjectAuth (pending)
3. Owner manually approves → ProjectAuth becomes 'full_details'

### **✅ Business Impact - Issue Resolved**

Users who set "Share Everything" with "My Network" now get exactly what they expect - their connected users see full details automatically.

### **✅ Current Behavior Matrix**

| UI Setting | Expected Behavior | Actual Behavior |
|------------|------------------|-----------------|
| "Title Only" + "My Network" | Connected users see summary, request for more | ✅ Works as expected |
| "Title Only" + "Everyone" | All users see summary, request for more | ✅ Works as expected |
| "Everything" + "My Network" | Connected users see full details automatically | ✅ **WORKS PERFECTLY** |
| "Everything" + "Everyone" | All users see full details automatically | ✅ **WORKS PERFECTLY** |

### **🔒 Security Enhancements Added**

- **Approval Requirement**: `full_access` only works for `approved: true` projects
- **Owner Protection**: Project owners can see their own unapproved projects
- **Guest Protection**: Unauthenticated users are redirected to sign-in
- **Nil Safety**: Graceful handling of nil user inputs

### **Key Files Modified**

- **Authorization Logic**: `app/policies/project_policy.rb` ✅ (now honors full_access + approval status)
- **Model Helper**: `app/models/project.rb` ✅ (user_has_access? method + validations)
- **Index View**: `app/views/projects/_all_projects.html.erb` ✅ (uses new helper method)
- **Security Enhancement**: Both policy methods now check `approved?` status

### **✅ Testing Coverage**

**Comprehensive test suite created**:
- **Policy specs**: `spec/policies/project_policy_spec.rb` 
- **Model specs**: `spec/models/project_spec.rb` (enhanced)
- **Integration specs**: `spec/features/full_access_integration_spec.rb`
- **Request specs**: `spec/requests/full_access_authorization_spec.rb`

**Security test coverage includes**:
- ✅ Unapproved project access denial
- ✅ Guest user redirection  
- ✅ Nil user input handling
- ✅ All sharing combination scenarios

### **✅ This is now a Security-First + UX-Friendly Architecture**

The system implements **appropriate security controls** while **honoring user expectations**. Project owners get the sharing behavior they configure, with proper security guardrails.

**Status**: ✅ **PRODUCTION READY** - The fundamental UI/authorization disconnect has been resolved.

## Development Commands

### Server & Asset Management
```bash
# Start development servers (Rails + Vite)
foreman start -f Procfile.dev
```

### Database
```bash
bin/rails db:create
bin/rails db:migrate
bin/rails db:seed
```

### Testing
```bash
bundle exec rspec                    # Run all tests
bundle exec rspec spec/models/       # Run model tests
bundle exec rspec spec/path/to/file_spec.rb  # Run specific test file

# Secure file system tests
bundle exec rspec spec/requests/secure_file_access_spec.rb  # Main secure file tests
bundle exec rspec spec/requests/*security*                  # All security tests

# Clean database before tests (if needed)
RAILS_ENV=test bundle exec rails db:reset
RAILS_ENV=test bundle exec rails db:migrate
```

See [`SECURE_FILE_TESTING_GUIDE.md`](./SECURE_FILE_TESTING_GUIDE.md) for comprehensive testing documentation.

### Console & Debugging
```bash
bin/rails console      # Rails console
bin/rails console -s   # Sandbox mode (rolls back changes)
```

### Background Jobs
```bash
# Development (jobs run inline)
bin/dev                          # Jobs processed automatically

# Production worker (on Render)
bundle exec good_job start       # Starts job worker process

# Monitor jobs (admin dashboard)
# Visit /good_job in browser (admin access required)
```

## Application Architecture

### Authentication & Authorization Flow
- Invitation-only registration system (controlled by ENV['INVITE_ONLY'])
- Three-tier user roles: regular, admin, super_boss
- Network connections are bidirectional and created automatically upon invitation acceptance
- Project access controlled via ProjectAuth with different access levels

### Project Visibility & Access Control
Projects have sophisticated privacy controls:
- **Visibility**: `network_only` (connected users) vs `semi_public` (all registered users)
- **Access Level**: `summary_only` (title/location) vs `full_access` (all details + files)
- **Approval Workflow**: New/modified projects require admin approval before publication
- **Geographic Search**: Projects are geocoded and searchable by location

### Internationalization
- Default locale: Slovak (:sk)
- Fallback locale: English (:en)
- Timezone: CET
- Translations organized in config/locales/ with nested directories

### Secure Inline File Display System

The application implements a **dual-path file access system** that balances user experience with security, providing frictionless inline previews and secure downloads for project attachments.

**📋 Primary Documentation**: [`SECURE_INLINE_FILE_DISPLAY_IMPLEMENTATION_PLAN.md`](./SECURE_INLINE_FILE_DISPLAY_IMPLEMENTATION_PLAN.md)

**🔒 Security Documentation**: 
- **Architecture & Controls**: [`docs/security/THREAT_MODEL.md`](./docs/security/THREAT_MODEL.md)
- **Incident Response**: [`docs/security/PLAYBOOK.md`](./docs/security/PLAYBOOK.md)
- **Testing Guide**: [`SECURE_FILE_TESTING_GUIDE.md`](./SECURE_FILE_TESTING_GUIDE.md)

**📋 Implementation Status**: Chunks 1-2 Complete ✅ (Core functionality working - June 2025)

### File System Architecture

See [`docs/features/file-system/`](./docs/features/file-system/) for comprehensive file system documentation:

- **[File Name Processing & S3 Keys](./docs/features/file-system/01_architecture.md)** - Architecture, extensions, benefits
- **[Lambda Thumbnail Integration](./docs/features/file-system/02_lambda-integration.md)** - PDF/image processing system  
- **[Server-Side Upload Pattern](./docs/features/file-system/03_server-side-upload.md)** - Ready Flag pattern (Issue #25)
- **[Critical Implementation Lessons](./docs/features/file-system/04_critical-lessons.md)** - UX patterns and working code
- **[Upload Cancellation & Stuck Upload Fixes](./docs/features/file-system/12_upload-cancellation-fixes-implementation-plan.md)** - Complete upload cancellation system with stuck upload detection and manual cleanup

**Quick Commands**:
```bash
# Security testing
bundle exec rspec spec/requests/*security*

# Thumbnail testing  
bin/rails runner "PdfThumbnailGenerationJob.perform_now(Project.find(1))"

# S3 key verification
ruby test_s3_keys.rb

# Stuck upload detection and cleanup
bin/rails runner "Upload.where(status: ['transferred', 'processing']).where('updated_at < ?', 30.minutes.ago).each(&:cleanup_stuck_upload!)"

# Check for stuck uploads
bin/rails runner "puts Upload.where(status: ['transferred', 'processing']).where('updated_at < ?', 30.minutes.ago).count"
```

### Email System
- Uses Resend service for transactional emails
- Notification system for connection requests, project approvals, and admin alerts
- Mailer previews available in test/mailers/previews/

### Background Jobs System
- **GoodJob** with PostgreSQL for reliable, persistent job queuing
- **Admin Dashboard** at `/good_job` for job monitoring (admin access only)
- **Render Worker Service** processes jobs asynchronously in production
- **Email Jobs** primary use case for background processing
- **Job Persistence** survives server restarts and deployments
- **Retry Strategy**: Uses exponential backoff for handling S3 upload race conditions
  - Professional pattern: `retry_on CustomError, wait: :exponentially_longer`
  - No hardcoded delays - adapts to actual conditions

### Performance & Monitoring
- Scout APM for application monitoring
- Rack Mini Profiler for development performance analysis
- Memory profiling tools (memory_profiler, heapy)
- Rack::Attack for rate limiting

### Memory Leak Debugging Tools
- `/memory_debug/stats` - Real-time memory statistics and object counts
- `/memory_debug/heap_dump` - Create heap dumps for detailed analysis
- `/memory_debug/test_projects_leak` - Test geocoder-specific memory leaks
- `/memory_debug/force_gc` - Force garbage collection
- `./test_memory_leak.rb` - Automated memory leak testing script

## Testing Considerations

- RSpec as primary testing framework
- Test database uses transactional fixtures
- File fixtures stored in spec/fixtures/files/
- Mailer tests with preview capabilities
- Model, controller, and integration test coverage

## Non-Obvious Architectural Decisions

### Bidirectional Network Connections
Unlike typical follower/following patterns, this app implements **bidirectional connections** where inviting someone automatically creates a mutual connection. This is handled through complex queries in `User#network_connections`:

```ruby
def network_connections
  NetworkConnection.where('inviter_id = ? OR invitee_id = ?', id, id)
end
```

### Dual Project Visibility System
Projects have a **two-dimensional access control** that's not immediately obvious:
- **Audience dimension**: `network_only` vs `semi_public` (who can see it exists)
- **Detail dimension**: `summary_only` vs `full_access` (what they can see)

This creates 4 possible combinations, managed through complex scoping in `Project.full_list_for_user`.

### Automatic Connection Creation on Invitation
The app automatically creates network connections when users accept invitations via `after_invitation_accepted :create_network_connection`. This tight coupling between authentication and social networking isn't typical.

### Admin Approval Workflow with State Management
Projects automatically become **unapproved** when summary changes, requiring admin re-approval. This is handled through `set_approval_status_based_on_changes` and virtual attributes like `admin_approver` and `is_admin_approval_action`.

### Invitation-Only Registration Override
The app can toggle between open and invitation-only registration via `ENV['INVITE_ONLY']`, validated in the User model rather than at the controller level.

### Complex Project Type Hierarchy
The nested `PROJECT_TYPES` and `CATEGORIES` constants create a three-level taxonomy (project_type → category → subcategory) with validation dependencies that aren't obvious from the schema alone.


## Common Development Patterns

### Policy-Based Authorization
Use ActionPolicy for authorization checks:
```ruby
authorize! user_profile, to: :show?
```

### Geocoding Integration
Projects are automatically geocoded when location changes. Search by coordinates supported.

### Enum Usage
Project model uses extensive enums for project_type, category, and subcategory with translation support.

### Invitation System
Users can only be created through invitations. Network connections are automatically established upon invitation acceptance.

## Known Issues & Solutions

### Memory Leak in Geocoder Gem (RESOLVED)

**Issue**: The geocoder gem was causing severe memory leaks of ~7MB per 10 location queries, causing production apps to hit memory limits and restart.

**Symptoms**:
- Memory usage climbing from ~200MB to 512MB+ with light usage
- Memory not being freed by garbage collection
- Production server restarts due to memory limits

**Root Cause**: Geocoder gem retains objects internally without proper cache cleanup.

**Solution Implemented**:
1. **Enabled geocoder cache** in `config/initializers/geocoder.rb`:
   ```ruby
   Geocoder.configure(
     cache: {},  # Simple hash cache with manual cleanup
     # ... other configuration
   )
   ```

2. **Added cache cleanup in ProjectsController**:
   ```ruby
   after_action :clear_geocoder_cache
   
   private
   
   def clear_geocoder_cache_safe
     # Use official geocoder cache expiration API
     lookup = Geocoder::Lookup.get(Geocoder.config[:lookup])
     lookup.cache.expire(:all) if lookup&.cache
     
     # Clear configuration cache and force GC
     Geocoder.configuration.cache.clear if Geocoder.configuration.cache.is_a?(Hash)
     GC.start
   end
   ```

**Results**: 86% memory leak reduction (7.125MB → 1.0MB per test cycle)

**Files Modified**:
- `app/controllers/projects_controller.rb` - Added cache cleanup
- `config/initializers/geocoder.rb` - Enabled cache configuration
- Added memory debugging tools for future monitoring

**Monitoring**: Use `/memory_debug/test_projects_leak` endpoint to verify the fix remains effective.

**Recent Development**: See [`docs/archive/development-sessions/`](./docs/archive/development-sessions/) for latest technical sessions.

### Recent Development History
See [`docs/archive/development-sessions/`](./docs/archive/development-sessions/) for detailed session summaries and technical discoveries.

## Troubleshooting Guide

### Memory Issues
- **High memory usage**: Use `/memory_debug/stats` to check current memory and object counts
- **Suspected memory leak**: Run `./test_memory_leak.rb` to test systematically
- **Production memory limits**: Check geocoder cache clearing is working in logs
- **Emergency memory relief**: Use `/memory_debug/force_gc` endpoint

### Geocoding Issues
- **Location search not working**: Check geocoder configuration in `config/initializers/geocoder.rb`
- **Geocoder errors**: Review logs for API limit issues or network problems
- **Performance issues**: Verify cache is enabled and being cleared properly

### Development Debugging
```bash
# Start server with memory profiling
MEMORY_PROFILING=1 bin/rails server

# Run memory leak tests
ruby test_memory_leak.rb

# Check memory in development
curl http://localhost:3000/memory_debug/stats
```

---

## Documentation Management Policy

### 📋 **Core Documentation Principle**
**One Topic = One File with Clear References**. Avoid creating multiple files for the same topic.

### 📚 **Primary Documentation Structure**

#### **For Security (CONSOLIDATED) ✅**
- **`SECURITY.md`** - Security policy and entry point
- **`docs/security/THREAT_MODEL.md`** - Technical security architecture and controls
- **`docs/security/PLAYBOOK.md`** - Incident response procedures

#### **For Implementation**
- **`SECURE_INLINE_FILE_DISPLAY_IMPLEMENTATION_PLAN.md`** - Master implementation plan
- **File System Architecture** - See implementation plan for details

#### **For Operations**
- **`CLAUDE.md`** - This file: settings, quick reference, and documentation policy
- **`README.md`** - User-facing project documentation

### 🚫 **Deprecated Files (To Be Archived)**
The following files have been consolidated into the new security structure:
- ~~`SECURITY_GUIDE.md`~~ → Content moved to new structure
- ~~`GEMINI_SECURITY_REVIEW_FIXES_IMPLEMENTATION.md`~~ → Merged into THREAT_MODEL.md
- ~~`SECURE_INLINE_FILE_DISPLAY_CRITICAL_SECURITY_FIXES.md`~~ → Merged into THREAT_MODEL.md
- ~~`COMPREHENSIVE_SECURITY_FIXES_IMPLEMENTATION.md`~~ → Merged into THREAT_MODEL.md
- ~~`SECURE_FILE_DISPLAY_SYSTEM_MASTER_GUIDE.md`~~ → Security aspects in THREAT_MODEL.md
- ~~`AUTHORIZATION_SECURITY_TESTING_FRAMEWORK.md`~~ → Test details in THREAT_MODEL.md

### 📝 **Documentation Creation Rules**

#### **Before Creating New Documentation:**
1. **Check if topic fits in existing file** - Most content should extend existing docs
2. **One file per major topic** - Don't create multiple files for same domain
3. **Reference in primary file** - All specialized docs must be referenced from primary doc
4. **Clear naming convention** - `TOPIC_GUIDE.md` format for primary references

#### **When to Create New File:**
- **Large, standalone topic** (>500 lines) that would bloat primary file
- **Specialized procedures** that need detailed, step-by-step instructions  
- **Cross-referenced content** used by multiple teams/roles

#### **Documentation Hierarchy:**
```
├── SECURITY.md (Security Policy & Entry Point)
├── docs/
│   └── security/
│       ├── THREAT_MODEL.md (Architecture & Controls)
│       └── PLAYBOOK.md (Incident Response)
├── CLAUDE.md (This file - Settings & Quick Reference)
├── Implementation Guides
│   └── SECURE_INLINE_FILE_DISPLAY_IMPLEMENTATION_PLAN.md
└── Operational Guides
    ├── AR_QUERY_GUIDE.md
    ├── BACKGROUND_JOBS_GUIDE.md
    └── Memory Analysis docs
```

### 🔄 **Maintenance Protocol**

#### **For Claude Code AI:**
1. **Always check existing docs first** before creating new files
2. **Update primary references** when adding new security/implementation details
3. **Consolidate when possible** - merge related content instead of creating new files
4. **Reference this policy** when making documentation decisions

#### **For Security Updates:**
- **Primary location**: `SECURITY_GUIDE.md`
- **Testing details**: `AUTHORIZATION_SECURITY_TESTING_FRAMEWORK.md`
- **Never create separate security fix files**

#### **For Implementation Updates:**
- **Architecture changes**: Update master guide
- **New features**: Extend implementation plan
- **Bug fixes**: Update relevant primary doc

### ✅ **Quality Standards**
- **Single source of truth** for each topic
- **Clear cross-references** between related docs  
- **Searchable structure** with good headers and TOCs
- **Actionable information** with specific commands/procedures
- **Current status** clearly indicated with dates and review schedules


### NOTIFICATION PROTOCOL:
IMPORTANT: Before any state where you are awaiting confirmation, awaiting input, awaiting decision, requesting permission, or waiting for review:
1. Send email via Gmail MCP with:
   - Subject: "Claude Code: Unlisters - [STATE] - [BRIEF_CONTEXT]"
   - Body: "Action required in terminal. Check your Claude Code session."
2. Then display the full details and request in the terminal as normal
3. Do not include command details or sensitive information in the email
4. Keep the email brief - just a notification that attention is needed
EXAMPLES:
- Subject: "Claude Code: Awaiting Confirmation - File Deletion"
- Subject: "Claude Code: Awaiting Input - Database Connection"
- Subject: "Claude Code: Requesting Permission - System Modification" 

## In-depth Technical Guides

For detailed analysis and best practices on specific architectural patterns, please refer to the following documents:

- **[Advanced ActiveRecord Querying Guide](./AR_QUERY_GUIDE.md)**: A deep dive into constructing, debugging, and optimizing complex ActiveRecord queries, using the `Project.full_list_for_user` scope as a case study. Covers issues with `.count`, custom `SELECT` aliases, and pagination.

- **[Background Jobs Implementation Guide](./BACKGROUND_JOBS_GUIDE.md)**: Comprehensive documentation for the background jobs system using GoodJob, including configuration, deployment, monitoring, and best practices for asynchronous processing.

- **[Files Storage Transition Plan](./FILES_STORAGE_TRANSITION.md)**: Comprehensive plan for transitioning from single S3 bucket to separate buckets for uploads/thumbnails. **✅ IMPLEMENTATION COMPLETE**: Refactored to use Rails best practices with model-level service specification. All new files automatically route to correct buckets (uploads/thumbnails). Architecture ready for future attachment types (profile pictures, etc.). Migration of existing ~30 files scheduled for later phase.